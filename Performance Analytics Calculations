### Performance Analytics Calculations

#### Average Completed Orders Per Day
```dart
// Formula: Average Completed = Total Completed Orders / Active Days
static double calculateAvgCompleted({
  required int sumCompleted,
  required int activeDays,
}) {
  if (activeDays == 0) return 0;
  return sumCompleted / activeDays;
}
```

**Source of `sumCompleted`:**
The `sumCompleted` value is obtained from the **last 14 days of order data** (excluding the current day). This calculation is performed by:

1. **Date Range Calculation**: 
   ```dart
   // If endDate is March 25, 2025, we want orders from March 11, 2025 to March 24, 2025
   final adjustedEndDate = endDate.subtract(const Duration(days: 1)); // Exclude current date
   final startDate = adjustedEndDate.subtract(const Duration(days: 13)); // 14 days total
   ```

2. **Database Query**: 
   ```dart
   Future<Either<Failure, List<Order>>> getOrdersForPerformanceCalculation(DateTime endDate) async {
     // Calculate the date range for the last 14 days (not including the current date)
     final adjustedEndDate = endDate.subtract(const Duration(days: 1)); // Exclude current date
     final startDate = adjustedEndDate.subtract(const Duration(days: 13)); // 14 days total
     
     return await getOrdersForDateRange(startDate, adjustedEndDate);
   }
   ```

3. **Sum Calculation**:
   ```dart
   Future<Either<Failure, int>> getTotalCompletedOrdersForLast14Days(DateTime endDate) async {
     final ordersResult = await getOrdersForPerformanceCalculation(endDate);
     
     return ordersResult.fold(
       (failure) => Left(failure),
       (orders) {
         // Calculate total completed orders
         int totalOrdersCompleted = 0;
         for (final order in orders) {
           totalOrdersCompleted += order.orderCompleted;
         }
         return Right(totalOrdersCompleted);
       },
     );
   }
   ```

**Business Logic:**
- **14-Day Window**: Uses the last 14 days of order data to provide a stable performance baseline
- **Excludes Current Day**: The current day is excluded to avoid incomplete data
- **Performance Baseline**: This 14-day window serves as the foundation for calculating average performance metrics
- **Real-time Updates**: The calculation is updated whenever new order data is entered

#### Average Online Hours Per Day
```dart
// Formula: Average Online = Total Online Hours / Active Days
static double calculateAvgOnline({
  required double onlineHours,
  required int activeDays,
}) {
  if (activeDays == 0) return 0;
  return onlineHours / activeDays;
}
```

#### Retention Rate Calculation
```dart
// Formula: Retention = (Average Online Hours * 60) / Average Completed Orders
static double calculateRetention({
  required double avgOnline,
  required double avgCompleted,
}) {
  if (avgCompleted == 0) return 0;
  return (avgOnline * 60) / avgCompleted;
}
```

**Business Logic:**
- Measures how efficiently the driver uses online time
- Higher retention indicates better order completion efficiency
- Used for level progression and performance evaluation

